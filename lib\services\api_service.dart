import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

class ApiService {
  late final Dio _dio;

  // Flask server URL - Update this with your actual server
  static const String baseUrl = 'http://********:5000'; // For Android emulator
  // static const String baseUrl = 'http://localhost:5000'; // For iOS simulator
  // static const String baseUrl = 'http://YOUR_SERVER_IP:5000'; // For real device
  // static const String baseUrl = 'https://your-domain.com'; // For production

  ApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(
          minutes: 5,
        ), // AI processing can take time
        sendTimeout: const Duration(minutes: 2),
      ),
    );

    // Add interceptors for logging (optional)
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: false, // Don't log large image data
        responseBody: false,
      ),
    );
  }

  Future<String> processImage({
    required String imagePath,
    required String maskPath,
  }) async {
    try {
      // Prepare multipart form data
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(imagePath, filename: 'image.jpg'),
        'mask': await MultipartFile.fromFile(maskPath, filename: 'mask.png'),
      });

      // Send request to Flask backend
      final response = await _dio.post(
        '/process-image',
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          responseType:
              ResponseType.bytes, // Important: Tell Dio to expect bytes
        ),
      );

      if (response.statusCode == 200) {
        // Save the processed image
        List<int> processedImageData;

        try {
          if (response.data is List<int>) {
            processedImageData = response.data as List<int>;
          } else if (response.data is Uint8List) {
            processedImageData = response.data as Uint8List;
          } else {
            throw Exception(
              'Unexpected response data type: ${response.data.runtimeType}',
            );
          }
        } catch (e) {
          throw Exception('Failed to process response data: $e');
        }

        final tempDir = await getTemporaryDirectory();
        final processedImagePath =
            '${tempDir.path}/processed_${DateTime.now().millisecondsSinceEpoch}.jpg';

        final processedImageFile = File(processedImagePath);
        await processedImageFile.writeAsBytes(
          Uint8List.fromList(processedImageData),
        );

        return processedImagePath;
      } else {
        throw Exception('Server returned status code: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception(
          'Connection timeout. Please check your internet connection.',
        );
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception(
          'Processing timeout. The image might be too large or complex.',
        );
      } else if (e.response?.statusCode == 400) {
        throw Exception('Invalid image or mask format.');
      } else if (e.response?.statusCode == 500) {
        throw Exception('Server error. Please try again later.');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  Future<bool> checkServerHealth() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<Map<String, dynamic>> getServerInfo() async {
    try {
      final response = await _dio.get('/info');
      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('Failed to get server info');
      }
    } catch (e) {
      throw Exception('Server info error: $e');
    }
  }

  Future<List<String>> getSupportedModels() async {
    try {
      final response = await _dio.get('/models');
      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return List<String>.from(data['models'] ?? []);
      } else {
        throw Exception('Failed to get supported models');
      }
    } catch (e) {
      throw Exception('Models error: $e');
    }
  }
}
