#!/usr/bin/env python3
"""
Quick test script for the AI Object Removal API
This script tests the backend API endpoints
"""

import requests
import json
import os
from PIL import Image, ImageDraw
import io

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get('http://localhost:5000/health')
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_models():
    """Test models endpoint"""
    try:
        response = requests.get('http://localhost:5000/models')
        if response.status_code == 200:
            print("✅ Models endpoint passed")
            print(f"   Available models: {response.json()}")
            return True
        else:
            print(f"❌ Models endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Models endpoint error: {e}")
        return False

def create_test_image():
    """Create a simple test image"""
    # Create a simple test image
    img = Image.new('RGB', (200, 200), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # Draw a simple shape to remove
    draw.rectangle([50, 50, 150, 150], fill='red', outline='black')
    draw.text((75, 100), "REMOVE", fill='white')
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def create_test_mask():
    """Create a simple test mask"""
    # Create mask (white area will be inpainted)
    mask = Image.new('RGB', (200, 200), color='black')
    draw = ImageDraw.Draw(mask)
    
    # White rectangle where the red square is
    draw.rectangle([50, 50, 150, 150], fill='white')
    
    # Save to bytes
    mask_bytes = io.BytesIO()
    mask.save(mask_bytes, format='PNG')
    mask_bytes.seek(0)
    
    return mask_bytes

def test_process_image():
    """Test image processing endpoint"""
    try:
        print("🧪 Testing image processing...")
        
        # Create test image and mask
        test_image = create_test_image()
        test_mask = create_test_mask()
        
        # Prepare files for upload
        files = {
            'image': ('test_image.jpg', test_image, 'image/jpeg'),
            'mask': ('test_mask.png', test_mask, 'image/png')
        }
        
        # Send request
        response = requests.post('http://localhost:5000/process-image', files=files)
        
        if response.status_code == 200:
            print("✅ Image processing passed")
            
            # Save the result
            with open('test_result.jpg', 'wb') as f:
                f.write(response.content)
            print("   Result saved as 'test_result.jpg'")
            return True
        else:
            print(f"❌ Image processing failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Image processing error: {e}")
        return False

def main():
    print("🚀 Testing AI Object Removal API")
    print("=" * 40)
    
    # Test all endpoints
    tests = [
        ("Health Check", test_health),
        ("Models Endpoint", test_models),
        ("Image Processing", test_process_image)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! API is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the server logs.")
    
    print("\n💡 Next steps:")
    print("   1. If tests passed, try the Flutter app")
    print("   2. If tests failed, check server logs")
    print("   3. Make sure the server is running on port 5000")

if __name__ == "__main__":
    main()
